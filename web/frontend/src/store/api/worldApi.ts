import { apiSlice } from './apiSlice'
import type { GameWorld } from '../slices/gameSlice'

// 世界相关的类型定义
export interface CreateWorldRequest {
  name: string
  description: string
  theme: string
  is_public: boolean
  max_players: number
  world_settings?: Record<string, any>
}

export interface UpdateWorldRequest {
  name?: string
  description?: string
  theme?: string
  is_public?: boolean
  max_players?: number
  world_settings?: Record<string, any>
}

export interface WorldResponse {
  success: boolean
  message: string
  data: GameWorld
}

export interface WorldListResponse {
  success: boolean
  message: string
  data: {
    worlds: GameWorld[]
    total: number
    page: number
    limit: number
  }
}

export interface WorldStateResponse {
  success: boolean
  message: string
  data: {
    world_id: string
    current_time: string
    game_time: number
    weather: string
    season: string
    global_events: Array<{
      id: string
      event_type: string
      name: string
      status: string
      priority: number
    }>
    active_scenes: Array<{
      id: string
      name: string
      status: string
      character_count: number
    }>
    online_characters: Array<{
      id: string
      name: string
      character_type: string
      current_scene_id: string
    }>
    variables: Record<string, any>
  }
}

export interface UpdateWorldTimeRequest {
  minutes: number
}

// 扩展 API slice 添加世界管理端点
export const worldApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // 创建世界
    createWorld: builder.mutation<WorldResponse, CreateWorldRequest>({
      query: (body) => ({
        url: '/game/worlds',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['World'],
    }),

    // 获取世界详情
    getWorld: builder.query<WorldResponse, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}`,
        method: 'GET',
      }),
      providesTags: (_result, _error, worldId) => [{ type: 'World', id: worldId }],
    }),

    // 更新世界
    updateWorld: builder.mutation<WorldResponse, { worldId: string; data: UpdateWorldRequest }>({
      query: ({ worldId, data }) => ({
        url: `/game/worlds/${worldId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (_result, _error, { worldId }) => [{ type: 'World', id: worldId }],
    }),

    // 删除世界
    deleteWorld: builder.mutation<{ success: boolean; message: string }, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['World'],
    }),

    // 加入世界
    joinWorld: builder.mutation<{ success: boolean; message: string }, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}/join`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, worldId) => [{ type: 'World', id: worldId }],
    }),

    // 离开世界
    leaveWorld: builder.mutation<{ success: boolean; message: string }, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}/leave`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, worldId) => [{ type: 'World', id: worldId }],
    }),

    // 获取我的世界列表
    getMyWorlds: builder.query<WorldListResponse, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 10 } = {}) => ({
        url: `/game/my-worlds?page=${page}&limit=${limit}`,
        method: 'GET',
      }),
      providesTags: ['World'],
    }),

    // 获取公开世界列表
    getPublicWorlds: builder.query<WorldListResponse, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 10 } = {}) => ({
        url: `/game/public-worlds?page=${page}&limit=${limit}`,
        method: 'GET',
      }),
      providesTags: ['World'],
    }),

    // 获取世界状态
    getWorldState: builder.query<WorldStateResponse, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}/state`,
        method: 'GET',
      }),
      providesTags: (_result, _error, worldId) => [{ type: 'World', id: `${worldId}-state` }],
    }),

    // 更新世界时间
    updateWorldTime: builder.mutation<{ success: boolean; message: string }, { 
      worldId: string
      data: UpdateWorldTimeRequest 
    }>({
      query: ({ worldId, data }) => ({
        url: `/game/worlds/${worldId}/time`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (_result, _error, { worldId }) => [
        { type: 'World', id: worldId },
        { type: 'World', id: `${worldId}-state` }
      ],
    }),

    // 处理世界时钟周期
    processWorldTick: builder.mutation<{ success: boolean; message: string }, string>({
      query: (worldId) => ({
        url: `/game/worlds/${worldId}/tick`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, worldId) => [
        { type: 'World', id: worldId },
        { type: 'World', id: `${worldId}-state` },
        'Character',
        'Scene',
        'Event'
      ],
    }),
  }),
})

// 导出生成的hooks
export const {
  useCreateWorldMutation,
  useGetWorldQuery,
  useLazyGetWorldQuery,
  useUpdateWorldMutation,
  useDeleteWorldMutation,
  useJoinWorldMutation,
  useLeaveWorldMutation,
  useGetMyWorldsQuery,
  useLazyGetMyWorldsQuery,
  useGetPublicWorldsQuery,
  useLazyGetPublicWorldsQuery,
  useGetWorldStateQuery,
  useLazyGetWorldStateQuery,
  useUpdateWorldTimeMutation,
  useProcessWorldTickMutation,
} = worldApi
