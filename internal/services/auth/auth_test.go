package auth

import (
	"testing"
	"time"

	"ai-text-game-iam-npc/internal/auth"
	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 创建测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// 手动创建简化的用户表（SQLite兼容）
	err = db.Exec(`
		CREATE TABLE users (
			id TEXT PRIMARY KEY,
			external_id TEXT NOT NULL,
			external_provider TEXT NOT NULL,
			email TEXT NOT NULL,
			display_name TEXT,
			avatar_url TEXT,
			game_roles TEXT DEFAULT '["user"]',
			status TEXT DEFAULT 'active',
			preferences TEXT DEFAULT '{}',
			id_p_claims TEXT DEFAULT '{}',
			created_at DATETIME,
			updated_at DATETIME,
			last_login_at DATETIME,
			deleted_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建用户统计表
	err = db.Exec(`
		CREATE TABLE user_stats (
			user_id TEXT PRIMARY KEY,
			total_play_time INTEGER DEFAULT 0,
			worlds_created INTEGER DEFAULT 0,
			worlds_joined INTEGER DEFAULT 0,
			achievements TEXT DEFAULT '[]',
			level INTEGER DEFAULT 1,
			experience INTEGER DEFAULT 0,
			updated_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	return db
}

// setupTestConfig 创建测试配置
func setupTestConfig() *config.Config {
	return &config.Config{
		Auth: config.AuthConfig{
			JWTSecret:     "test-secret-key-for-testing-only",
			JWTExpiration: 24 * time.Hour,
			OAuth: config.OAuthConfig{
				Providers: map[string]config.OAuthProvider{
					"google": {
						ClientID:     "test-google-client-id",
						ClientSecret: "test-google-client-secret",
						RedirectURL:  "http://localhost:3000/auth/callback/google",
					},
					"github": {
						ClientID:     "test-github-client-id",
						ClientSecret: "test-github-client-secret",
						RedirectURL:  "http://localhost:3000/auth/callback/github",
					},
				},
			},
		},
	}
}

func TestNewService(t *testing.T) {
	cfg := setupTestConfig()
	db := setupTestDB(t)

	service := auth.NewService(cfg, db)

	assert.NotNil(t, service)
}

func TestGetAuthURL(t *testing.T) {
	cfg := setupTestConfig()
	db := setupTestDB(t)
	service := auth.NewService(cfg, db)

	// 测试获取Google认证URL
	authURL, err := service.GetAuthURL("google", "test-state")
	assert.NoError(t, err)
	assert.NotEmpty(t, authURL)
	assert.Contains(t, authURL, "client_id")

	// 测试不支持的提供商
	_, err = service.GetAuthURL("unsupported", "test-state")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "不支持的认证提供商")
}

func TestValidateToken(t *testing.T) {
	cfg := setupTestConfig()
	db := setupTestDB(t)
	service := auth.NewService(cfg, db)

	// 测试无效令牌
	claims, err := service.ValidateToken("invalid-token")
	assert.Error(t, err)
	assert.Nil(t, claims)
}

func TestGetUserByID(t *testing.T) {
	cfg := setupTestConfig()
	db := setupTestDB(t)
	service := auth.NewService(cfg, db)

	// 创建测试用户
	displayName := "测试用户"
	avatarURL := "https://example.com/avatar.jpg"
	user := &models.User{
		ExternalID:       "google-123",
		ExternalProvider: "google",
		Email:            "<EMAIL>",
		DisplayName:      &displayName,
		AvatarURL:        &avatarURL,
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON{},
		IDPClaims:        models.JSON{},
	}
	err := db.Create(user).Error
	require.NoError(t, err)

	// 测试获取存在的用户
	foundUser, err := service.GetUserByID(user.ID)
	assert.NoError(t, err)
	assert.NotNil(t, foundUser)
	assert.Equal(t, user.ID, foundUser.ID)
	assert.Equal(t, user.Email, foundUser.Email)

	// 测试获取不存在的用户
	notFoundUser, err := service.GetUserByID(uuid.New())
	assert.Error(t, err)
	assert.Nil(t, notFoundUser)
}

func TestGetProviders(t *testing.T) {
	cfg := setupTestConfig()
	db := setupTestDB(t)
	service := auth.NewService(cfg, db)

	providers := service.GetProviders()
	assert.NotEmpty(t, providers)
	assert.Contains(t, providers, "google")
	assert.Contains(t, providers, "github")
}
