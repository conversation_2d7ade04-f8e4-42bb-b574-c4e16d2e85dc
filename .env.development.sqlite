# 开发环境配置文件 - SQLite版本
# 用于跳过身份认证和使用SQLite数据库

# 服务器配置
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=120s
ENVIRONMENT=development

# 认证跳过配置
SKIP_AUTH=true

# 数据库配置 - 开发环境使用SQLite
# 注意：DB_NAME以.db结尾会自动使用SQLite驱动
DB_HOST=
DB_PORT=
DB_USER=
DB_PASSWORD=
DB_NAME=dev.db
DB_SSL_MODE=development
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_MAX_LIFETIME=5m

# 数据库兼容性配置
DB_TYPE=sqlite
DB_AUTO_MIGRATE=true

# Redis配置 - 开发环境（SQLite模式下禁用）
# 注意：当前项目的业务逻辑不依赖Redis，可以安全禁用
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=2
REDIS_ENABLED=false

# JWT认证配置 - 开发环境（实际不会使用）
JWT_SECRET=dev-secret-key-not-used-in-dev-mode
JWT_EXPIRATION=24h

# OAuth配置 - 开发环境（可选配置）
OAUTH_GOOGLE_CLIENT_ID=
OAUTH_GOOGLE_CLIENT_SECRET=
OAUTH_GOOGLE_REDIRECT_URL=http://localhost:8080/auth/google/callback

OAUTH_GITHUB_CLIENT_ID=
OAUTH_GITHUB_CLIENT_SECRET=
OAUTH_GITHUB_REDIRECT_URL=http://localhost:8080/auth/github/callback

# AI服务配置 - 开发环境启用Mock
AI_BASE_URL=https://wm.atjog.com
AI_TOKEN=dev_token
AI_TIMEOUT=30s
AI_MAX_RETRIES=3
AI_RETRY_DELAY=1s
AI_QUEUE_SIZE=1000
AI_BATCH_SIZE=10
AI_BATCH_TIMEOUT=5s
AI_MOCK_ENABLED=true

# 游戏配置 - 开发环境放宽限制
GAME_MAX_WORLDS_PER_USER=100
GAME_MAX_PLAYERS_PER_WORLD=100
GAME_DEFAULT_TIME_RATE=1.0
GAME_TICK_INTERVAL=10s
GAME_MAX_MEMORY_PER_CHAR=1000
GAME_MAX_EXPERIENCE_PER_CHAR=10000

# 开发环境特殊配置
DEV_ENABLE_DEBUG_LOGS=true
DEV_ENABLE_CORS_ALL=true
DEV_DISABLE_RATE_LIMIT=true

# SQLite特有配置
SQLITE_JOURNAL_MODE=WAL
SQLITE_SYNCHRONOUS=NORMAL
SQLITE_CACHE_SIZE=10000
