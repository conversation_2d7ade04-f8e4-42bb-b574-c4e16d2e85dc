import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import styled from 'styled-components'
import { motion } from 'framer-motion'

// 页面组件
import LoginPage from './pages/LoginPage'
import AuthCallbackPage from './pages/AuthCallbackPage'
import GameLobbyPage from './pages/GameLobbyPage'
import WorldCreatePage from './pages/WorldCreatePage'
import GamePage from './pages/GamePage'
import ProfilePage from './pages/ProfilePage'

// 布局组件
import AppHeader from './components/layout/AppHeader'
import AppFooter from './components/layout/AppFooter'
import DevModeIndicator from './components/DevModeIndicator'

// 样式组件
const { Content } = Layout

const StyledLayout = styled(Layout)`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`

const StyledContent = styled(Content)`
  padding: 0;
  background: transparent;
`

const App: React.FC = () => {
  return (
    <StyledLayout>
      <AppHeader />
      <StyledContent>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Routes>
            {/* 默认路由重定向到登录页 */}
            <Route path="/" element={<Navigate to="/login" replace />} />

            {/* 认证相关路由 */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/auth/callback/:provider" element={<AuthCallbackPage />} />

            {/* 游戏相关路由 */}
            <Route path="/lobby" element={<GameLobbyPage />} />
            <Route path="/world/create" element={<WorldCreatePage />} />
            <Route path="/game/:worldId" element={<GamePage />} />

            {/* 用户相关路由 */}
            <Route path="/profile" element={<ProfilePage />} />

            {/* 404 页面 */}
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </motion.div>
      </StyledContent>
      <AppFooter />

      {/* 开发模式指示器 */}
      <DevModeIndicator />
    </StyledLayout>
  )
}

export default App
