# 前端开发环境配置

# 开发模式标识
NODE_ENV=development
VITE_APP_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:8080
VITE_API_TIMEOUT=30000

# 开发特性开关
VITE_DEV_MODE=true
VITE_ENABLE_DEV_TOOLS=true
VITE_SHOW_DEV_INDICATOR=true

# 认证配置
VITE_SKIP_AUTH=true
VITE_AUTO_LOGIN=true

# 调试配置
VITE_DEBUG_LOGS=true
VITE_VERBOSE_LOGS=true

# Mock配置
VITE_ENABLE_MOCK=false
VITE_MOCK_DELAY=500

# 热重载配置
VITE_HMR_PORT=3001
VITE_HMR_HOST=localhost
