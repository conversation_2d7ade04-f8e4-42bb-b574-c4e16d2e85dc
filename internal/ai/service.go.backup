package ai

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Service AI服务
type Service struct {
	config *config.Config
	db     *gorm.DB
	client *http.Client
	logger *logger.Logger
}

// NewService 创建AI服务
func NewService(cfg *config.Config, db *gorm.DB, logger *logger.Logger) *Service {
	return &Service{
		config: cfg,
		db:     db,
		client: &http.Client{
			Timeout: time.Duration(cfg.AI.Timeout) * time.Second,
		},
		logger: logger,
	}
}

// GenerateRequest AI生成请求
type GenerateRequest struct {
	Type           string                 `json:"type"`            // 生成类型：scene, character, event, dialogue等
	Prompt         string                 `json:"prompt"`          // 提示词
	Context        map[string]interface{} `json:"context"`         // 上下文信息
	Schema         map[string]interface{} `json:"schema"`          // 期望的响应结构
	MaxTokens      int                    `json:"max_tokens"`      // 最大token数
	Temperature    float64                `json:"temperature"`     // 温度参数
	WorldID        *uuid.UUID             `json:"world_id"`        // 世界ID
	UserID         *uuid.UUID             `json:"user_id"`         // 用户ID
}

// GenerateResponse AI生成响应
type GenerateResponse struct {
	Content      string                 `json:"content"`       // 生成的内容
	StructuredData map[string]interface{} `json:"structured_data"` // 结构化数据
	TokenUsage   int                    `json:"token_usage"`   // 使用的token数
	ResponseTime int                    `json:"response_time"` // 响应时间(毫秒)
}

// GenerateContent 生成内容
func (s *Service) GenerateContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	startTime := time.Now()
	
	// 记录AI交互日志
	interaction := &models.AIInteraction{
		WorldID:         req.WorldID,
		UserID:          req.UserID,
		InteractionType: req.Type,
		Prompt:          req.Prompt,
		ResponseSchema:  models.JSON(req.Schema),
		Status:          "pending",
	}
	
	if err := s.db.Create(interaction).Error; err != nil {
		s.logger.Error("创建AI交互记录失败", "error", err)
	}
	
	var response *GenerateResponse
	var err error
	
	// 根据配置决定使用真实API还是Mock
	if s.config.AI.MockEnabled {
		response, err = s.generateMockContent(req)
	} else {
		response, err = s.generateRealContent(ctx, req)
	}
	
	responseTime := int(time.Since(startTime).Milliseconds())
	
	// 更新交互记录
	if err != nil {
		interaction.SetFailed(s.db, err.Error())
	} else {
		responseJSON, _ := json.Marshal(response.StructuredData)
		interaction.SetCompleted(s.db, string(responseJSON), response.TokenUsage, responseTime)
	}
	
	if response != nil {
		response.ResponseTime = responseTime
	}
	
	return response, err
}

// generateRealContent 调用真实AI API生成内容
func (s *Service) generateRealContent(ctx context.Context, req *GenerateRequest) (*GenerateResponse, error) {
	// 构建API请求
	apiRequest := map[string]interface{}{
		"model":       s.config.AI.Model,
		"prompt":      req.Prompt,
		"max_tokens":  req.MaxTokens,
		"temperature": req.Temperature,
		"context":     req.Context,
		"schema":      req.Schema,
	}
	
	requestBody, err := json.Marshal(apiRequest)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}
	
	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", s.config.AI.APIEndpoint, bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	
	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+s.config.AI.APIKey)
	
	// 发送请求
	resp, err := s.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送AI API请求失败: %w", err)
	}
	defer resp.Body.Close()
	
	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取AI API响应失败: %w", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("AI API返回错误状态: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}
	
	// 解析响应
	var apiResponse struct {
		Content        string                 `json:"content"`
		StructuredData map[string]interface{} `json:"structured_data"`
		TokenUsage     int                    `json:"token_usage"`
	}
	
	if err := json.Unmarshal(responseBody, &apiResponse); err != nil {
		return nil, fmt.Errorf("解析AI API响应失败: %w", err)
	}
	
	return &GenerateResponse{
		Content:        apiResponse.Content,
		StructuredData: apiResponse.StructuredData,
		TokenUsage:     apiResponse.TokenUsage,
	}, nil
}

// generateMockContent 生成Mock内容
func (s *Service) generateMockContent(req *GenerateRequest) (*GenerateResponse, error) {
	s.logger.Info("使用Mock模式生成AI内容", "type", req.Type)
	
	switch req.Type {
	case "scene":
		return s.generateMockScene(req)
	case "character":
		return s.generateMockCharacter(req)
	case "event":
		return s.generateMockEvent(req)
	case "dialogue":
		return s.generateMockDialogue(req)
	case "description":
		return s.generateMockDescription(req)
	default:
		return s.generateMockGeneral(req)
	}
}

// generateMockScene 生成Mock场景
func (s *Service) generateMockScene(req *GenerateRequest) (*GenerateResponse, error) {
	mockScenes := []map[string]interface{}{
		{
			"name":        "神秘森林",
			"description": "一片古老而神秘的森林，高大的树木遮天蔽日，林间弥漫着淡淡的雾气。偶尔能听到远处传来的鸟鸣声和树叶沙沙作响的声音。",
			"type":        "forest",
			"atmosphere":  "mysterious",
			"connections": map[string]string{
				"north": "山洞入口",
				"south": "村庄广场",
				"east":  "河边小径",
			},
			"entities": []string{"古老的橡树", "发光的蘑菇", "小溪"},
		},
		{
			"name":        "废弃城堡",
			"description": "一座年代久远的城堡，石墙斑驳，藤蔓缠绕。城堡内部阴暗潮湿，回音在空旷的大厅中回荡。",
			"type":        "castle",
			"atmosphere":  "eerie",
			"connections": map[string]string{
				"west":  "城堡花园",
				"north": "塔楼",
				"down":  "地下室",
			},
			"entities": []string{"破损的盔甲", "古老的画像", "蜘蛛网"},
		},
	}
	
	// 随机选择一个场景
	selectedScene := mockScenes[time.Now().Unix()%int64(len(mockScenes))]
	
	return &GenerateResponse{
		Content:        selectedScene["description"].(string),
		StructuredData: selectedScene,
		TokenUsage:     150,
	}, nil
}

// generateMockCharacter 生成Mock角色
func (s *Service) generateMockCharacter(req *GenerateRequest) (*GenerateResponse, error) {
	mockCharacters := []map[string]interface{}{
		{
			"name":        "艾莉娅",
			"description": "一位年轻的精灵法师，有着银色的长发和翠绿的眼睛。她穿着一件深蓝色的法袍，手持一根镶嵌着蓝宝石的法杖。",
			"type":        "npc",
			"personality": []string{"智慧", "善良", "好奇"},
			"skills":      []string{"魔法", "草药学", "古代语言"},
			"background":  "来自精灵王国的年轻法师，正在寻找失落的魔法知识。",
		},
		{
			"name":        "格雷戈里",
			"description": "一位经验丰富的人类战士，身材魁梧，脸上有着战斗留下的伤疤。他身穿重甲，背负着一把巨大的双手剑。",
			"type":        "npc",
			"personality": []string{"勇敢", "忠诚", "直率"},
			"skills":      []string{"剑术", "战术", "领导力"],
			"background":  "曾经的王国骑士，现在是一名雇佣兵，为了正义而战。",
		},
	}

	selectedCharacter := mockCharacters[time.Now().Unix()%int64(len(mockCharacters))]

	return &GenerateResponse{
		Content:        selectedCharacter["description"].(string),
		StructuredData: selectedCharacter,
		TokenUsage:     120,
	}, nil
}

// generateMockEvent 生成Mock事件
func (s *Service) generateMockEvent(req *GenerateRequest) (*GenerateResponse, error) {
	mockEvents := []map[string]interface{}{
		{
			"name":        "神秘商人的到来",
			"description": "一位穿着华丽长袍的神秘商人来到了村庄，他的马车上装满了奇异的物品和魔法道具。",
			"type":        "encounter",
			"priority":    5,
			"duration":    30,
			"effects": map[string]interface{}{
				"new_items":     []string{"魔法药水", "古老地图", "神秘护符"},
				"new_quests":    []string{"寻找失落的宝藏"},
				"reputation":    10,
			},
		},
		{
			"name":        "暴风雨来袭",
			"description": "天空突然乌云密布，雷声轰鸣，一场猛烈的暴风雨即将来临。所有户外活动都必须暂停。",
			"type":        "weather",
			"priority":    3,
			"duration":    60,
			"effects": map[string]interface{}{
				"weather_change": "storm",
				"visibility":     "low",
				"movement_speed": 0.5,
			},
		},
	}
	
	selectedEvent := mockEvents[time.Now().Unix()%int64(len(mockEvents))]
	
	return &GenerateResponse{
		Content:        selectedEvent["description"].(string),
		StructuredData: selectedEvent,
		TokenUsage:     100,
	}, nil
}

// generateMockDialogue 生成Mock对话
func (s *Service) generateMockDialogue(req *GenerateRequest) (*GenerateResponse, error) {
	mockDialogues := []string{
		"欢迎来到我们的村庄，陌生人。你看起来像是从很远的地方来的。",
		"这里最近发生了一些奇怪的事情，也许你能帮助我们解决这个问题。",
		"小心那片森林，据说里面住着危险的生物。",
		"你有什么需要的吗？我这里有各种各样的物品。",
		"传说中的宝藏就在那座古老的城堡里，但是没有人敢去寻找。",
	}
	
	selectedDialogue := mockDialogues[time.Now().Unix()%int64(len(mockDialogues))]
	
	return &GenerateResponse{
		Content: selectedDialogue,
		StructuredData: map[string]interface{}{
			"dialogue": selectedDialogue,
			"emotion":  "neutral",
			"intent":   "information",
		},
		TokenUsage: 50,
	}, nil
}

// generateMockDescription 生成Mock描述
func (s *Service) generateMockDescription(req *GenerateRequest) (*GenerateResponse, error) {
	descriptions := []string{
		"这是一个充满魔法和奇迹的世界，古老的传说在这里成为现实。",
		"微风轻抚过草地，带来了远方花朵的香气。",
		"夕阳西下，金色的光芒洒在大地上，一切都显得那么宁静美好。",
		"古老的石碑上刻着神秘的符文，似乎在诉说着久远的故事。",
		"篝火在夜晚中跳跃着，温暖的光芒驱散了黑暗和寒冷。",
	}
	
	selectedDescription := descriptions[time.Now().Unix()%int64(len(descriptions))]
	
	return &GenerateResponse{
		Content: selectedDescription,
		StructuredData: map[string]interface{}{
			"description": selectedDescription,
			"mood":        "peaceful",
			"style":       "descriptive",
		},
		TokenUsage: 80,
	}, nil
}

// generateMockGeneral 生成通用Mock内容
func (s *Service) generateMockGeneral(req *GenerateRequest) (*GenerateResponse, error) {
	return &GenerateResponse{
		Content: fmt.Sprintf("这是一个关于%s的Mock响应。在实际环境中，这里会调用真实的AI API来生成内容。", req.Type),
		StructuredData: map[string]interface{}{
			"type":    req.Type,
			"mock":    true,
			"prompt":  req.Prompt,
			"context": req.Context,
		},
		TokenUsage: 75,
	}, nil
}

// GetInteractionHistory 获取AI交互历史
func (s *Service) GetInteractionHistory(worldID *uuid.UUID, userID *uuid.UUID, limit int) ([]models.AIInteraction, error) {
	var interactions []models.AIInteraction
	query := s.db.Order("created_at DESC")
	
	if worldID != nil {
		query = query.Where("world_id = ?", *worldID)
	}
	
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Find(&interactions).Error
	return interactions, err
}

// GetTokenUsageStats 获取token使用统计
func (s *Service) GetTokenUsageStats(worldID *uuid.UUID, userID *uuid.UUID, days int) (map[string]interface{}, error) {
	var stats struct {
		TotalInteractions int `json:"total_interactions"`
		TotalTokens       int `json:"total_tokens"`
		AvgTokensPerReq   int `json:"avg_tokens_per_request"`
	}
	
	query := s.db.Model(&models.AIInteraction{}).Where("status = ?", "completed")
	
	if worldID != nil {
		query = query.Where("world_id = ?", *worldID)
	}
	
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}
	
	if days > 0 {
		query = query.Where("created_at >= ?", time.Now().AddDate(0, 0, -days))
	}
	
	// 获取总交互数
	query.Count(&stats.TotalInteractions)
	
	// 获取总token数
	var totalTokens sql.NullInt64
	query.Select("SUM(token_usage)").Scan(&totalTokens)
	stats.TotalTokens = int(totalTokens.Int64)
	
	// 计算平均值
	if stats.TotalInteractions > 0 {
		stats.AvgTokensPerReq = stats.TotalTokens / stats.TotalInteractions
	}
	
	return map[string]interface{}{
		"total_interactions":     stats.TotalInteractions,
		"total_tokens":          stats.TotalTokens,
		"avg_tokens_per_request": stats.AvgTokensPerReq,
	}, nil
}
