package migration

import (
	"fmt"
	"os"
	"path/filepath"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/pkg/database"

	"gorm.io/gorm"
)

// SmartMigrator 智能迁移器，根据数据库类型选择合适的迁移策略
type SmartMigrator struct {
	db             *gorm.DB
	config         *config.DatabaseConfig
	compatibility  *database.CompatibilityConfig
	migrationsPath string
}

// NewSmartMigrator 创建智能迁移器
func NewSmartMigrator(db *gorm.DB, cfg *config.DatabaseConfig, migrationsPath string) *SmartMigrator {
	return &SmartMigrator{
		db:             db,
		config:         cfg,
		compatibility:  database.NewCompatibilityConfig(db),
		migrationsPath: migrationsPath,
	}
}

// AutoMigrate 自动迁移，使用GORM的AutoMigrate功能
func (sm *SmartMigrator) AutoMigrate(models ...interface{}) error {
	// 使用GORM的AutoMigrate功能，它会自动处理不同数据库的差异
	return sm.db.AutoMigrate(models...)
}

// GetMigrationsPath 获取适合当前数据库类型的迁移文件路径
func (sm *SmartMigrator) GetMigrationsPath() string {
	dbType := database.GetDatabaseType(sm.db)
	
	var subPath string
	switch dbType {
	case database.SQLite:
		subPath = "sqlite"
	case database.PostgreSQL:
		subPath = "postgres"
	default:
		subPath = "postgres" // 默认使用PostgreSQL
	}
	
	fullPath := filepath.Join(sm.migrationsPath, subPath)
	
	// 检查路径是否存在，如果不存在则使用通用路径
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return sm.migrationsPath
	}
	
	return fullPath
}

// CreateMigrator 创建传统的迁移器
func (sm *SmartMigrator) CreateMigrator() (*Migrator, error) {
	migrationsPath := sm.GetMigrationsPath()
	return New(sm.config, migrationsPath)
}

// ValidateSchema 验证数据库模式
func (sm *SmartMigrator) ValidateSchema(models ...interface{}) error {
	// 检查每个模型的表是否存在
	for _, model := range models {
		if !sm.db.Migrator().HasTable(model) {
			return fmt.Errorf("表不存在，需要运行迁移: %T", model)
		}
	}
	return nil
}

// GetDatabaseInfo 获取数据库信息
func (sm *SmartMigrator) GetDatabaseInfo() map[string]interface{} {
	dbType := database.GetDatabaseType(sm.db)
	
	info := map[string]interface{}{
		"database_type":     string(dbType),
		"migrations_path":   sm.GetMigrationsPath(),
		"supports_jsonb":    sm.compatibility.SupportsJSONB(),
		"supports_uuid":     sm.compatibility.SupportsUUIDType(),
		"supports_triggers": sm.compatibility.SupportsTriggers(),
	}
	
	return info
}

// CreateTables 创建表（开发模式快速启动）
func (sm *SmartMigrator) CreateTables(models ...interface{}) error {
	// 在开发模式下，可以直接使用GORM创建表
	if sm.config.SSLMode == "development" || sm.config.Host == "" {
		return sm.AutoMigrate(models...)
	}
	
	// 生产模式下建议使用正式的迁移文件
	return fmt.Errorf("生产模式下请使用迁移文件创建表")
}

// DropTables 删除表（危险操作，仅用于测试）
func (sm *SmartMigrator) DropTables(models ...interface{}) error {
	for _, model := range models {
		if err := sm.db.Migrator().DropTable(model); err != nil {
			return fmt.Errorf("删除表失败 %T: %w", model, err)
		}
	}
	return nil
}

// CheckCompatibility 检查数据库兼容性
func (sm *SmartMigrator) CheckCompatibility() error {
	return sm.compatibility.ValidateCompatibility()
}

// TransformModelTags 转换模型标签以适配当前数据库
// 注意：这个功能需要在编译时或运行时动态修改GORM标签，比较复杂
// 建议使用条件编译或配置文件的方式来处理
func (sm *SmartMigrator) GetModelTagSuggestions() map[string]string {
	suggestions := make(map[string]string)
	
	switch sm.compatibility.DBType {
	case database.SQLite:
		suggestions["uuid_primary_key"] = "type:text;primary_key"
		suggestions["json_field"] = "type:text"
		suggestions["timestamp_field"] = "type:datetime"
	case database.PostgreSQL:
		suggestions["uuid_primary_key"] = "type:uuid;primary_key;default:gen_random_uuid()"
		suggestions["json_field"] = "type:jsonb"
		suggestions["timestamp_field"] = "type:timestamp with time zone"
	}
	
	return suggestions
}

// ExecuteRawSQL 执行原始SQL（会根据数据库类型进行转换）
func (sm *SmartMigrator) ExecuteRawSQL(sql string) error {
	transformedSQL := sm.compatibility.TransformSQL(sql)
	
	// 跳过注释行
	if len(transformedSQL) > 2 && transformedSQL[:2] == "--" {
		return nil
	}
	
	return sm.db.Exec(transformedSQL).Error
}

// CreateDevelopmentSchema 创建开发环境的数据库模式
func (sm *SmartMigrator) CreateDevelopmentSchema(models ...interface{}) error {
	// 1. 首先尝试自动迁移
	if err := sm.AutoMigrate(models...); err != nil {
		return fmt.Errorf("自动迁移失败: %w", err)
	}
	
	// 2. 创建必要的触发器和函数（如果数据库支持）
	if sm.compatibility.SupportsTriggers() {
		// 为支持的表创建更新时间触发器
		tables := []string{"users", "user_stats", "worlds", "characters", "scenes"}
		
		// 先创建函数（PostgreSQL需要）
		if sm.compatibility.DBType == database.PostgreSQL {
			functionSQL := sm.compatibility.GetUpdateTimestampFunctionSQL()
			if err := sm.ExecuteRawSQL(functionSQL); err != nil {
				// 函数可能已存在，忽略错误
			}
		}
		
		// 创建触发器
		for _, table := range tables {
			if sm.db.Migrator().HasTable(table) {
				triggerSQL := sm.compatibility.GetTriggerSQL(table)
				if err := sm.ExecuteRawSQL(triggerSQL); err != nil {
					// 触发器可能已存在，忽略错误
				}
			}
		}
	}
	
	return nil
}

// GetMigrationStatus 获取迁移状态
func (sm *SmartMigrator) GetMigrationStatus() (map[string]interface{}, error) {
	status := map[string]interface{}{
		"database_type": string(sm.compatibility.DBType),
		"auto_migrate":  true, // 表示使用了GORM AutoMigrate
	}
	
	// 尝试获取传统迁移器的状态
	migrator, err := sm.CreateMigrator()
	if err == nil {
		defer migrator.Close()
		
		if version, dirty, err := migrator.Version(); err == nil {
			status["migration_version"] = version
			status["migration_dirty"] = dirty
		}
	}
	
	return status, nil
}
