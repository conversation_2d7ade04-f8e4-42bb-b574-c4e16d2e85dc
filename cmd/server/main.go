package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/pkg/database"
	"ai-text-game-iam-npc/internal/routes"
	"ai-text-game-iam-npc/pkg/logger"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	logger := logger.New("info")
	logger.Info("正在启动AI文本游戏服务器", "version", "1.0.0")

	// 连接数据库
	db, err := database.New(&cfg.Database)
	if err != nil {
		logger.Fatal("连接数据库失败", "error", err)
	}
	logger.Info("数据库连接成功")

	// TODO: 实现数据库迁移
	// if err := database.Migrate(db); err != nil {
	//     logger.Fatal("数据库迁移失败", "error", err)
	// }
	logger.Info("数据库迁移跳过（待实现）")

	// 设置路由
	router := routes.SetupRoutes(cfg, db, logger)

	// 设置静态文件服务
	routes.SetupStaticRoutes(router)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%s", cfg.Server.Port),
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		logger.Info("服务器启动", "port", cfg.Server.Port, "environment", cfg.Server.Environment)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("服务器启动失败", "error", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logger.Info("正在关闭服务器...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优雅关闭服务器
	if err := server.Shutdown(ctx); err != nil {
		logger.Error("服务器强制关闭", "error", err)
	} else {
		logger.Info("服务器已优雅关闭")
	}
}
