{"name": "ai-text-game-frontend", "version": "0.1.0", "private": true, "description": "AI文本游戏前端应用", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.0.0", "antd": "^5.0.0", "styled-components": "^5.3.0", "framer-motion": "^8.0.0", "axios": "^1.3.0", "@ant-design/icons": "^5.0.0", "lucide-react": "^0.300.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/styled-components": "^5.1.0", "typescript": "^4.9.0", "vite": "^4.0.0", "@vitejs/plugin-react": "^3.0.0", "@types/node": "^18.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "ts-jest": "^29.1.0", "identity-obj-proxy": "^3.0.0", "jest-transform-stub": "^2.0.0"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}