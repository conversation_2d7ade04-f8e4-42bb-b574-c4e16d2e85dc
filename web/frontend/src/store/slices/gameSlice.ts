import { createSlice, PayloadAction } from '@reduxjs/toolkit'

// 游戏世界接口
export interface GameWorld {
  id: string
  name: string
  description: string
  theme: string
  isPublic: boolean
  maxPlayers: number
  currentPlayers: number
  creatorId: string
  createdAt: string
  updatedAt: string
}

// 游戏角色接口
export interface GameCharacter {
  id: string
  name: string
  description: string
  attributes: Record<string, any>
  worldId: string
  userId: string
  currentSceneId: string
  createdAt: string
  updatedAt: string
}

// 游戏场景接口
export interface GameScene {
  id: string
  name: string
  description: string
  worldId: string
  connections: string[]
  characters: string[]
  createdAt: string
  updatedAt: string
}

// 游戏事件接口
export interface GameEvent {
  id: string
  type: string
  content: string
  worldId: string
  sceneId?: string
  characterId?: string
  timestamp: string
}

// 游戏状态接口
export interface GameState {
  // 当前游戏世界
  currentWorld: GameWorld | null
  currentCharacter: GameCharacter | null
  currentScene: GameScene | null
  
  // 游戏数据
  worlds: GameWorld[]
  characters: GameCharacter[]
  scenes: GameScene[]
  events: GameEvent[]
  
  // UI 状态
  isInGame: boolean
  isLoading: boolean
  error: string | null
  
  // 聊天和交互
  chatMessages: Array<{
    id: string
    type: 'user' | 'system' | 'ai'
    content: string
    timestamp: string
  }>
}

// 初始状态
const initialState: GameState = {
  currentWorld: null,
  currentCharacter: null,
  currentScene: null,
  worlds: [],
  characters: [],
  scenes: [],
  events: [],
  isInGame: false,
  isLoading: false,
  error: null,
  chatMessages: [],
}

// 创建游戏 slice
export const gameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    // 设置当前世界
    setCurrentWorld: (state, action: PayloadAction<GameWorld | null>) => {
      state.currentWorld = action.payload
    },
    
    // 设置当前角色
    setCurrentCharacter: (state, action: PayloadAction<GameCharacter | null>) => {
      state.currentCharacter = action.payload
    },
    
    // 设置当前场景
    setCurrentScene: (state, action: PayloadAction<GameScene | null>) => {
      state.currentScene = action.payload
    },
    
    // 进入游戏
    enterGame: (state) => {
      state.isInGame = true
    },
    
    // 离开游戏
    leaveGame: (state) => {
      state.isInGame = false
      state.currentWorld = null
      state.currentCharacter = null
      state.currentScene = null
      state.chatMessages = []
    },
    
    // 更新世界列表
    setWorlds: (state, action: PayloadAction<GameWorld[]>) => {
      state.worlds = action.payload
    },
    
    // 添加世界
    addWorld: (state, action: PayloadAction<GameWorld>) => {
      state.worlds.push(action.payload)
    },
    
    // 更新角色列表
    setCharacters: (state, action: PayloadAction<GameCharacter[]>) => {
      state.characters = action.payload
    },
    
    // 添加角色
    addCharacter: (state, action: PayloadAction<GameCharacter>) => {
      state.characters.push(action.payload)
    },
    
    // 更新场景列表
    setScenes: (state, action: PayloadAction<GameScene[]>) => {
      state.scenes = action.payload
    },
    
    // 添加场景
    addScene: (state, action: PayloadAction<GameScene>) => {
      state.scenes.push(action.payload)
    },
    
    // 添加事件
    addEvent: (state, action: PayloadAction<GameEvent>) => {
      state.events.push(action.payload)
    },
    
    // 添加聊天消息
    addChatMessage: (state, action: PayloadAction<{
      type: 'user' | 'system' | 'ai'
      content: string
    }>) => {
      const message = {
        id: Date.now().toString(),
        ...action.payload,
        timestamp: new Date().toISOString(),
      }
      state.chatMessages.push(message)
    },
    
    // 清除聊天消息
    clearChatMessages: (state) => {
      state.chatMessages = []
    },
    
    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null
    },
  },
})

// 导出 actions
export const {
  setCurrentWorld,
  setCurrentCharacter,
  setCurrentScene,
  enterGame,
  leaveGame,
  setWorlds,
  addWorld,
  setCharacters,
  addCharacter,
  setScenes,
  addScene,
  addEvent,
  addChatMessage,
  clearChatMessages,
  setLoading,
  setError,
  clearError,
} = gameSlice.actions

// 导出 selectors
export const selectGame = (state: { game: GameState }) => state.game
export const selectCurrentWorld = (state: { game: GameState }) => state.game.currentWorld
export const selectCurrentCharacter = (state: { game: GameState }) => state.game.currentCharacter
export const selectCurrentScene = (state: { game: GameState }) => state.game.currentScene
export const selectIsInGame = (state: { game: GameState }) => state.game.isInGame
export const selectWorlds = (state: { game: GameState }) => state.game.worlds
export const selectCharacters = (state: { game: GameState }) => state.game.characters
export const selectChatMessages = (state: { game: GameState }) => state.game.chatMessages
export const selectGameLoading = (state: { game: GameState }) => state.game.isLoading
export const selectGameError = (state: { game: GameState }) => state.game.error

export default gameSlice.reducer
