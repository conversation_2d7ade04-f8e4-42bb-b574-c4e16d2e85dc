import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { RootState } from '../index'

// 基础查询配置
const baseQuery = fetchBaseQuery({
  baseUrl: 'http://localhost:8082/api/v1',
  prepareHeaders: (headers, { getState }) => {
    // 从 Redux state 中获取 token
    const token = (getState() as RootState).auth.token
    
    // 如果有 token，添加到请求头
    if (token) {
      headers.set('authorization', `Bearer ${token}`)
    }
    
    // 设置内容类型
    headers.set('content-type', 'application/json')
    
    return headers
  },
})

// 带有重新认证的查询
const baseQueryWithReauth = async (args: any, api: any, extraOptions: any) => {
  let result = await baseQuery(args, api, extraOptions)
  
  // 如果收到 401 错误，尝试刷新 token
  if (result.error && result.error.status === 401) {
    const refreshToken = (api.getState() as RootState).auth.refreshToken
    
    if (refreshToken) {
      // 尝试刷新 token
      const refreshResult = await baseQuery(
        {
          url: '/auth/refresh',
          method: 'POST',
          body: { refreshToken },
        },
        api,
        extraOptions
      )
      
      if (refreshResult.data) {
        // 刷新成功，重新发送原始请求
        result = await baseQuery(args, api, extraOptions)
      } else {
        // 刷新失败，登出用户
        api.dispatch({ type: 'auth/logout' })
      }
    } else {
      // 没有刷新 token，登出用户
      api.dispatch({ type: 'auth/logout' })
    }
  }
  
  return result
}

// 创建 API slice
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'User',
    'World',
    'Character',
    'Scene',
    'Event',
    'Validation',
  ],
  endpoints: () => ({}),
})

// 导出 hooks
export const {} = apiSlice
