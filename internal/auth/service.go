package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/internal/utils"

	"github.com/google/uuid"
	"golang.org/x/oauth2"
	"gorm.io/gorm"
)

// Service 认证服务
type Service struct {
	db         *gorm.DB
	jwtManager *utils.JWTManager
	providers  map[string]*oauth2.Config
}

// UserInfo 用户信息结构
type UserInfo struct {
	ID          string `json:"id"`
	Email       string `json:"email"`
	Name        string `json:"name"`
	AvatarURL   string `json:"avatar_url"`
	Provider    string `json:"provider"`
	AccessToken string `json:"access_token"`
}

// NewService 创建认证服务
func NewService(cfg *config.Config, db *gorm.DB) *Service {
	jwtManager := utils.NewJWTManager(cfg.Auth.JWTSecret, cfg.Auth.JWTExpiration)

	providers := make(map[string]*oauth2.Config)

	// 配置OAuth提供商
	for name, provider := range cfg.Auth.OAuth.Providers {
		providers[name] = &oauth2.Config{
			ClientID:     provider.ClientID,
			ClientSecret: provider.ClientSecret,
			RedirectURL:  provider.RedirectURL,
			Scopes:       provider.Scopes,
			Endpoint: oauth2.Endpoint{
				AuthURL:  provider.AuthURL,
				TokenURL: provider.TokenURL,
			},
		}
	}

	return &Service{
		db:         db,
		jwtManager: jwtManager,
		providers:  providers,
	}
}

// GetAuthURL 获取认证URL
func (s *Service) GetAuthURL(provider, state string) (string, error) {
	config, exists := s.providers[provider]
	if !exists {
		return "", fmt.Errorf("不支持的认证提供商: %s", provider)
	}

	return config.AuthCodeURL(state, oauth2.AccessTypeOffline), nil
}

// HandleCallback 处理OAuth回调
func (s *Service) HandleCallback(provider, code, state string) (*models.User, string, error) {
	config, exists := s.providers[provider]
	if !exists {
		return nil, "", fmt.Errorf("不支持的认证提供商: %s", provider)
	}

	// 交换授权码获取token
	ctx := context.Background()
	token, err := config.Exchange(ctx, code)
	if err != nil {
		return nil, "", fmt.Errorf("交换token失败: %w", err)
	}

	// 获取用户信息
	userInfo, err := s.getUserInfo(provider, token.AccessToken)
	if err != nil {
		return nil, "", fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 查找或创建用户
	user, err := s.findOrCreateUser(userInfo)
	if err != nil {
		return nil, "", fmt.Errorf("创建用户失败: %w", err)
	}

	// 更新最后登录时间
	if err := user.UpdateLastLogin(s.db); err != nil {
		return nil, "", fmt.Errorf("更新登录时间失败: %w", err)
	}

	// 生成JWT token
	jwtToken, err := s.jwtManager.GenerateToken(
		user.ID,
		user.Email,
		*user.DisplayName,
		user.ExternalID,
		user.ExternalProvider,
		user.GameRoles,
	)
	if err != nil {
		return nil, "", fmt.Errorf("生成JWT token失败: %w", err)
	}

	return user, jwtToken, nil
}

// ValidateToken 验证JWT token
func (s *Service) ValidateToken(tokenString string) (*utils.JWTClaims, error) {
	return s.jwtManager.ValidateToken(tokenString)
}

// RefreshToken 刷新JWT token
func (s *Service) RefreshToken(tokenString string) (string, error) {
	return s.jwtManager.RefreshToken(tokenString)
}

// GetUserByID 根据ID获取用户
func (s *Service) GetUserByID(userID uuid.UUID) (*models.User, error) {
	var user models.User
	err := s.db.Preload("Stats").First(&user, userID).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// getUserInfo 获取用户信息
func (s *Service) getUserInfo(provider, accessToken string) (*UserInfo, error) {
	switch provider {
	case "google":
		return s.getGoogleUserInfo(accessToken)
	case "github":
		return s.getGitHubUserInfo(accessToken)
	default:
		return nil, fmt.Errorf("不支持的提供商: %s", provider)
	}
}

// getGoogleUserInfo 获取Google用户信息
func (s *Service) getGoogleUserInfo(accessToken string) (*UserInfo, error) {
	resp, err := http.Get("https://www.googleapis.com/oauth2/v2/userinfo?access_token=" + accessToken)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var googleUser struct {
		ID      string `json:"id"`
		Email   string `json:"email"`
		Name    string `json:"name"`
		Picture string `json:"picture"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&googleUser); err != nil {
		return nil, err
	}

	return &UserInfo{
		ID:          googleUser.ID,
		Email:       googleUser.Email,
		Name:        googleUser.Name,
		AvatarURL:   googleUser.Picture,
		Provider:    "google",
		AccessToken: accessToken,
	}, nil
}

// getGitHubUserInfo 获取GitHub用户信息
func (s *Service) getGitHubUserInfo(accessToken string) (*UserInfo, error) {
	client := &http.Client{}
	req, err := http.NewRequest("GET", "https://api.github.com/user", nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "token "+accessToken)
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var githubUser struct {
		ID        int    `json:"id"`
		Login     string `json:"login"`
		Name      string `json:"name"`
		Email     string `json:"email"`
		AvatarURL string `json:"avatar_url"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&githubUser); err != nil {
		return nil, err
	}

	// 如果公开邮箱为空，尝试获取私有邮箱
	email := githubUser.Email
	if email == "" {
		email, _ = s.getGitHubUserEmail(accessToken)
	}

	name := githubUser.Name
	if name == "" {
		name = githubUser.Login
	}

	return &UserInfo{
		ID:          fmt.Sprintf("%d", githubUser.ID),
		Email:       email,
		Name:        name,
		AvatarURL:   githubUser.AvatarURL,
		Provider:    "github",
		AccessToken: accessToken,
	}, nil
}

// getGitHubUserEmail 获取GitHub用户邮箱
func (s *Service) getGitHubUserEmail(accessToken string) (string, error) {
	client := &http.Client{}
	req, err := http.NewRequest("GET", "https://api.github.com/user/emails", nil)
	if err != nil {
		return "", err
	}

	req.Header.Set("Authorization", "token "+accessToken)
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var emails []struct {
		Email   string `json:"email"`
		Primary bool   `json:"primary"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&emails); err != nil {
		return "", err
	}

	// 返回主邮箱
	for _, email := range emails {
		if email.Primary {
			return email.Email, nil
		}
	}

	// 如果没有主邮箱，返回第一个
	if len(emails) > 0 {
		return emails[0].Email, nil
	}

	return "", fmt.Errorf("未找到邮箱")
}

// findOrCreateUser 查找或创建用户
func (s *Service) findOrCreateUser(userInfo *UserInfo) (*models.User, error) {
	var user models.User

	// 先尝试根据外部ID和提供商查找用户
	err := s.db.Where("external_id = ? AND external_provider = ?",
		userInfo.ID, userInfo.Provider).First(&user).Error

	if err == nil {
		// 用户已存在，更新信息
		updates := map[string]interface{}{
			"email":        userInfo.Email,
			"display_name": userInfo.Name,
			"avatar_url":   userInfo.AvatarURL,
		}

		if err := s.db.Model(&user).Updates(updates).Error; err != nil {
			return nil, err
		}

		return &user, nil
	}

	if err != gorm.ErrRecordNotFound {
		return nil, err
	}

	// 用户不存在，创建新用户
	user = models.User{
		ExternalID:       userInfo.ID,
		ExternalProvider: userInfo.Provider,
		Email:            userInfo.Email,
		DisplayName:      &userInfo.Name,
		AvatarURL:        &userInfo.AvatarURL,
		GameRoles:        models.StringArray{"user"},
		Status:           "active",
		Preferences:      models.JSON{},
		IDPClaims:        models.JSON{},
	}

	if err := s.db.Create(&user).Error; err != nil {
		return nil, err
	}

	return &user, nil
}

// UpdateUserRoles 更新用户角色
func (s *Service) UpdateUserRoles(userID uuid.UUID, roles []string) error {
	return s.db.Model(&models.User{}).Where("id = ?", userID).
		Update("game_roles", models.StringArray(roles)).Error
}

// DeactivateUser 停用用户
func (s *Service) DeactivateUser(userID uuid.UUID) error {
	return s.db.Model(&models.User{}).Where("id = ?", userID).
		Update("status", "suspended").Error
}

// ActivateUser 激活用户
func (s *Service) ActivateUser(userID uuid.UUID) error {
	return s.db.Model(&models.User{}).Where("id = ?", userID).
		Update("status", "active").Error
}

// GetProviders 获取支持的认证提供商列表
func (s *Service) GetProviders() []string {
	providers := make([]string, 0, len(s.providers))
	for name := range s.providers {
		providers = append(providers, name)
	}
	return providers
}
