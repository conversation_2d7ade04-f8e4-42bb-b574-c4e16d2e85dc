package ai

import (
	"context"
	"testing"

	"ai-text-game-iam-npc/internal/config"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupTestDB 创建测试数据库
func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// 创建AI交互表
	err = db.Exec(`
		CREATE TABLE ai_interactions (
			id TEXT PRIMARY KEY,
			world_id TEXT,
			user_id TEXT,
			interaction_type TEXT NOT NULL,
			prompt TEXT NOT NULL,
			response TEXT,
			response_schema TEXT DEFAULT '{}',
			token_usage INTEGER DEFAULT 0,
			response_time INTEGER DEFAULT 0,
			status TEXT DEFAULT 'pending',
			error_message TEXT,
			created_at DATETIME,
			updated_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	return db
}

// setupTestAIService 创建测试AI服务
func setupTestAIService(t *testing.T) *Service {
	cfg := &config.Config{
		AI: config.AIConfig{
			MockEnabled: true, // 使用mock模式进行测试
		},
	}
	db := setupTestDB(t)
	log := logger.New("debug")
	
	service := NewService(cfg, db, log)
	return service
}

func TestNewAIService(t *testing.T) {
	cfg := &config.Config{
		AI: config.AIConfig{
			MockEnabled: true,
		},
	}
	db := setupTestDB(t)
	log := logger.New("debug")
	
	service := NewService(cfg, db, log)
	assert.NotNil(t, service)
}

func TestGenerateContent_Scene(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "scene",
		Prompt:      "生成一个神秘的森林场景",
		Context:     map[string]interface{}{"theme": "fantasy"},
		MaxTokens:   500,
		Temperature: 0.7,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
		},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}

func TestGenerateContent_Character(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "character",
		Prompt:      "生成一个精灵法师角色",
		Context:     map[string]interface{}{"theme": "fantasy"},
		MaxTokens:   400,
		Temperature: 0.8,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
		},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}

func TestGenerateContent_Event(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "event",
		Prompt:      "生成一个冒险事件",
		Context:     map[string]interface{}{"theme": "fantasy"},
		MaxTokens:   300,
		Temperature: 0.6,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"name":        "string",
			"description": "string",
			"type":        "string",
		},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}

func TestGenerateContent_Dialogue(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "dialogue",
		Prompt:      "生成村民的友好问候",
		Context:     map[string]interface{}{"theme": "fantasy"},
		MaxTokens:   200,
		Temperature: 0.9,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema: map[string]interface{}{
			"content": "string",
			"tone":    "string",
		},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}

func TestGenerateContent_General(t *testing.T) {
	service := setupTestAIService(t)
	ctx := context.Background()
	
	worldID := uuid.New()
	userID := uuid.New()
	
	req := &GenerateRequest{
		Type:        "unknown_type",
		Prompt:      "生成通用内容",
		Context:     map[string]interface{}{},
		MaxTokens:   100,
		Temperature: 0.5,
		WorldID:     &worldID,
		UserID:      &userID,
		Schema:      map[string]interface{}{},
	}
	
	response, err := service.GenerateContent(ctx, req)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.Greater(t, response.TokenUsage, 0)
	assert.NotNil(t, response.StructuredData)
}
