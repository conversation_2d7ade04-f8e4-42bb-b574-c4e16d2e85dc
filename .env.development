# 开发环境配置文件
# 用于跳过身份认证和API网关限制

# 服务器配置
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s
SERVER_IDLE_TIMEOUT=120s
ENVIRONMENT=development

# 认证跳过配置
SKIP_AUTH=true

# 数据库配置 - 开发环境
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=dev_password
DB_NAME=ai_text_game_dev
DB_SSL_MODE=disable
DB_MAX_OPEN_CONNS=25
DB_MAX_IDLE_CONNS=5
DB_MAX_LIFETIME=5m

# Redis配置 - 开发环境
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=2

# JWT认证配置 - 开发环境（实际不会使用）
JWT_SECRET=dev-secret-key-not-used-in-dev-mode
JWT_EXPIRATION=24h

# OAuth配置 - 开发环境（可选配置）
OAUTH_GOOGLE_CLIENT_ID=
OAUTH_GOOGLE_CLIENT_SECRET=
OAUTH_GOOGLE_REDIRECT_URL=http://localhost:8080/auth/google/callback

OAUTH_GITHUB_CLIENT_ID=
OAUTH_GITHUB_CLIENT_SECRET=
OAUTH_GITHUB_REDIRECT_URL=http://localhost:8080/auth/github/callback

# AI服务配置 - 开发环境启用Mock
AI_BASE_URL=https://wm.atjog.com
AI_TOKEN=dev_token
AI_TIMEOUT=30s
AI_MAX_RETRIES=3
AI_RETRY_DELAY=1s
AI_QUEUE_SIZE=1000
AI_BATCH_SIZE=10
AI_BATCH_TIMEOUT=5s
AI_MOCK_ENABLED=true

# 游戏配置 - 开发环境放宽限制
GAME_MAX_WORLDS_PER_USER=100
GAME_MAX_PLAYERS_PER_WORLD=100
GAME_DEFAULT_TIME_RATE=1.0
GAME_TICK_INTERVAL=10s
GAME_MAX_MEMORY_PER_CHAR=1000
GAME_MAX_EXPERIENCE_PER_CHAR=10000

# 开发环境特殊配置
DEV_ENABLE_DEBUG_LOGS=true
DEV_ENABLE_CORS_ALL=true
DEV_DISABLE_RATE_LIMIT=true
