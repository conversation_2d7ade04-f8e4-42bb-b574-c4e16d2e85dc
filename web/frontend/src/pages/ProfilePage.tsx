import React, { useEffect, useState } from 'react'
import {
  Card,
  Typography,
  Avatar,
  Descriptions,
  Tabs,
  List,
  Statistic,
  Row,
  Col,
  Tag,
  Button,
  Space,
  Spin,
  Empty,
  message
} from 'antd'
import {
  UserOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  EnvironmentOutlined,
  EditOutlined,
  LogoutOutlined
} from '@ant-design/icons'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { setPageTitle } from '../store/slices/uiSlice'
import {
  useGetUserProfileQuery,
  useGetMyWorldsQuery,
  useGetMyCharactersQuery,
  useGetTokenUsageQuery,
  useLogoutMutation
} from '../store/api'
import { AuthService } from '../services'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs

const Container = styled.div`
  min-height: calc(100vh - 64px - 70px);
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
`

const ProfileHeader = styled(Card)`
  margin-bottom: 24px;

  .ant-card-body {
    padding: 32px;
  }
`

const StatsCard = styled(Card)`
  height: 100%;

  .ant-statistic-content {
    color: #6366f1;
  }
`

const ProfilePage: React.FC = () => {
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { user } = useAppSelector(state => state.auth)
  const [logoutLoading, setLogoutLoading] = useState(false)

  // API hooks
  const { data: profile, isLoading: profileLoading } = useGetUserProfileQuery()
  const { data: myWorlds, isLoading: worldsLoading } = useGetMyWorldsQuery({})
  const { data: myCharacters, isLoading: charactersLoading } = useGetMyCharactersQuery({})
  const { data: tokenUsage, isLoading: usageLoading } = useGetTokenUsageQuery({})
  const [logoutMutation] = useLogoutMutation()

  useEffect(() => {
    dispatch(setPageTitle('个人资料'))
  }, [dispatch])

  // 处理登出
  const handleLogout = async () => {
    setLogoutLoading(true)
    try {
      await logoutMutation().unwrap()
      await AuthService.logout()
      message.success('已成功登出')
      navigate('/login')
    } catch (error) {
      console.error('登出失败:', error)
      // 即使API调用失败，也要清除本地状态
      await AuthService.logout()
      navigate('/login')
    } finally {
      setLogoutLoading(false)
    }
  }

  if (profileLoading) {
    return (
      <Container>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
          <Spin size="large" />
        </div>
      </Container>
    )
  }

  const userProfile = profile?.data || user

  return (
    <Container>
      <div style={{ maxWidth: 1200, margin: '0 auto' }}>
        {/* 用户信息头部 */}
        <ProfileHeader>
          <Row gutter={24} align="middle">
            <Col>
              <Avatar
                size={80}
                icon={<UserOutlined />}
              />
            </Col>
            <Col flex={1}>
              <Title level={2} style={{ margin: 0 }}>
                {userProfile?.email || '未知用户'}
              </Title>
              <Paragraph type="secondary" style={{ margin: '8px 0' }}>
                用户ID: {userProfile?.id || '未知'}
              </Paragraph>
              <Space>
                <Tag color="blue">
                  <ClockCircleOutlined style={{ marginRight: 4 }} />
                  注册时间: {userProfile?.createdAt ? new Date(userProfile.createdAt).toLocaleDateString() : '未知'}
                </Tag>
                <Tag color="green">
                  已认证用户
                </Tag>
              </Space>
            </Col>
            <Col>
              <Space>
                <Button icon={<EditOutlined />}>
                  编辑资料
                </Button>
                <Button
                  danger
                  icon={<LogoutOutlined />}
                  loading={logoutLoading}
                  onClick={handleLogout}
                >
                  登出
                </Button>
              </Space>
            </Col>
          </Row>
        </ProfileHeader>

        {/* 统计数据 */}
        <Row gutter={24} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <StatsCard>
              <Statistic
                title="创建的世界"
                value={myWorlds?.data?.worlds?.length || 0}
                prefix={<EnvironmentOutlined />}
                loading={worldsLoading}
              />
            </StatsCard>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <StatsCard>
              <Statistic
                title="拥有的角色"
                value={myCharacters?.data?.characters?.length || 0}
                prefix={<TeamOutlined />}
                loading={charactersLoading}
              />
            </StatsCard>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <StatsCard>
              <Statistic
                title="AI Token 使用"
                value={tokenUsage?.data?.total_tokens_used || 0}
                prefix={<TrophyOutlined />}
                loading={usageLoading}
              />
            </StatsCard>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <StatsCard>
              <Statistic
                title="游戏时长"
                value={0}
                suffix="小时"
                prefix={<ClockCircleOutlined />}
              />
            </StatsCard>
          </Col>
        </Row>

        {/* 详细信息标签页 */}
        <Card>
          <Tabs defaultActiveKey="worlds">
            <TabPane tab="我的世界" key="worlds">
              {worldsLoading ? (
                <div style={{ textAlign: 'center', padding: 40 }}>
                  <Spin size="large" />
                </div>
              ) : myWorlds?.data?.worlds && myWorlds.data.worlds.length > 0 ? (
                <List
                  dataSource={myWorlds.data.worlds}
                  renderItem={(world) => (
                    <List.Item
                      actions={[
                        <Button
                          type="link"
                          onClick={() => navigate(`/game/${world.id}`)}
                        >
                          进入游戏
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<EnvironmentOutlined />} />}
                        title={
                          <Space>
                            <span>{world.name}</span>
                            <Tag color="blue">{world.theme}</Tag>
                            {world.isPublic && <Tag color="green">公开</Tag>}
                          </Space>
                        }
                        description={
                          <div>
                            <Paragraph ellipsis={{ rows: 2 }}>
                              {world.description}
                            </Paragraph>
                            <Text type="secondary">
                              创建时间: {new Date(world.createdAt).toLocaleDateString()}
                            </Text>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <Empty
                  description="你还没有创建任何世界"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                >
                  <Button type="primary" onClick={() => navigate('/world/create')}>
                    创建第一个世界
                  </Button>
                </Empty>
              )}
            </TabPane>

            <TabPane tab="我的角色" key="characters">
              {charactersLoading ? (
                <div style={{ textAlign: 'center', padding: 40 }}>
                  <Spin size="large" />
                </div>
              ) : myCharacters?.data?.characters && myCharacters.data.characters.length > 0 ? (
                <List
                  dataSource={myCharacters.data.characters}
                  renderItem={(character) => (
                    <List.Item
                      actions={[
                        <Button
                          type="link"
                          onClick={() => navigate(`/game/${character.worldId}`)}
                        >
                          进入游戏
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<UserOutlined />} />}
                        title={
                          <Space>
                            <span>{character.name}</span>
                            <Tag>{'角色'}</Tag>
                          </Space>
                        }
                        description={
                          <div>
                            <Paragraph ellipsis={{ rows: 2 }}>
                              {character.description}
                            </Paragraph>
                            <Text type="secondary">
                              状态: 活跃 |
                              创建时间: {new Date(character.createdAt).toLocaleDateString()}
                            </Text>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <Empty
                  description="你还没有创建任何角色"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                >
                  <Button type="primary" onClick={() => navigate('/lobby')}>
                    去游戏大厅创建角色
                  </Button>
                </Empty>
              )}
            </TabPane>

            <TabPane tab="使用统计" key="stats">
              {usageLoading ? (
                <div style={{ textAlign: 'center', padding: 40 }}>
                  <Spin size="large" />
                </div>
              ) : (
                <Row gutter={24}>
                  <Col xs={24} md={12}>
                    <Card title="AI 使用统计" size="small">
                      <Descriptions column={1} size="small">
                        <Descriptions.Item label="总 Token 使用">
                          {tokenUsage?.data?.total_tokens_used || 0}
                        </Descriptions.Item>
                        <Descriptions.Item label="本月使用">
                          {tokenUsage?.data?.total_tokens_used || 0}
                        </Descriptions.Item>
                        <Descriptions.Item label="今日使用">
                          {tokenUsage?.data?.total_tokens_used || 0}
                        </Descriptions.Item>
                        <Descriptions.Item label="平均每次">
                          {Math.round((tokenUsage?.data?.total_tokens_used || 0) / 10) || 0}
                        </Descriptions.Item>
                      </Descriptions>
                    </Card>
                  </Col>
                  <Col xs={24} md={12}>
                    <Card title="游戏统计" size="small">
                      <Descriptions column={1} size="small">
                        <Descriptions.Item label="创建世界数">
                          {myWorlds?.data?.worlds?.length || 0}
                        </Descriptions.Item>
                        <Descriptions.Item label="角色数量">
                          {myCharacters?.data?.characters?.length || 0}
                        </Descriptions.Item>
                        <Descriptions.Item label="游戏时长">
                          0 小时
                        </Descriptions.Item>
                        <Descriptions.Item label="最后活动">
                          {userProfile?.updatedAt ? new Date(userProfile.updatedAt).toLocaleString() : '未知'}
                        </Descriptions.Item>
                      </Descriptions>
                    </Card>
                  </Col>
                </Row>
              )}
            </TabPane>
          </Tabs>
        </Card>
      </div>
    </Container>
  )
}

export default ProfilePage
