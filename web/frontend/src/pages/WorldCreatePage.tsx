import React, { useEffect, useState } from 'react'
import { Card, Typography, Form, Input, Select, Switch, Button, message, Space, Divider } from 'antd'
import { PlusOutlined, RobotOutlined } from '@ant-design/icons'
import styled from 'styled-components'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch } from '../store/hooks'
import { setPageTitle } from '../store/slices/uiSlice'
import { useCreateWorldMutation } from '../store/api'
import { useGenerateSceneMutation } from '../store/api'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

const Container = styled.div`
  min-height: calc(100vh - 64px - 70px);
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
`

const StyledCard = styled(Card)`
  width: 100%;
  max-width: 800px;

  .ant-card-body {
    padding: 32px;
  }
`

const AIButton = styled(Button)`
  border-color: #6366f1;
  color: #6366f1;

  &:hover {
    border-color: #5856eb;
    color: #5856eb;
  }
`

interface WorldFormData {
  name: string
  description: string
  theme: string
  setting: string
  isPublic: boolean
  maxPlayers: number
  rules?: string
}

const WorldCreatePage: React.FC = () => {
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const [form] = Form.useForm<WorldFormData>()
  const [loading, setLoading] = useState(false)
  const [aiGenerating, setAiGenerating] = useState(false)

  const [createWorld] = useCreateWorldMutation()
  const [generateScene] = useGenerateSceneMutation()

  useEffect(() => {
    dispatch(setPageTitle('创建世界'))
  }, [dispatch])

  // AI辅助生成世界描述
  const handleAIGenerate = async () => {
    const name = form.getFieldValue('name')
    const theme = form.getFieldValue('theme')
    const setting = form.getFieldValue('setting')

    if (!name || !theme) {
      message.warning('请先填写世界名称和类型')
      return
    }

    setAiGenerating(true)
    try {
      const result = await generateScene({
        world_id: 'temp', // 临时ID，因为世界还未创建
        scene_name: name,
        scene_type: 'main',
        theme: theme,
        mood: setting || 'neutral',
        special_requirements: `为名为"${name}"的${theme}类型世界生成详细描述，设定为${setting || '未指定'}`
      }).unwrap()

      if (result.data?.description) {
        form.setFieldsValue({
          description: result.data.description
        })
        message.success('AI生成完成！')
      }
    } catch (error) {
      console.error('AI生成失败:', error)
      message.error('AI生成失败，请稍后重试')
    } finally {
      setAiGenerating(false)
    }
  }

  // 提交表单
  const handleSubmit = async (values: WorldFormData) => {
    setLoading(true)
    try {
      const result = await createWorld({
        name: values.name,
        description: values.description,
        theme: values.theme,
        is_public: values.isPublic,
        max_players: values.maxPlayers,
        world_settings: {
          setting: values.setting,
          rules: values.rules || ''
        }
      }).unwrap()

      message.success('世界创建成功！')
      navigate(`/game/${result.data.id}`)
    } catch (error: any) {
      console.error('创建世界失败:', error)
      message.error(error?.data?.message || '创建世界失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Container>
      <StyledCard>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          <PlusOutlined style={{ marginRight: 8 }} />
          创建新世界
        </Title>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isPublic: false,
            maxPlayers: 10,
            theme: 'fantasy'
          }}
        >
          <Form.Item
            name="name"
            label="世界名称"
            rules={[
              { required: true, message: '请输入世界名称' },
              { min: 2, max: 50, message: '世界名称长度应在2-50个字符之间' }
            ]}
          >
            <Input placeholder="为你的世界起一个独特的名字" />
          </Form.Item>

          <Form.Item
            name="theme"
            label="世界类型"
            rules={[{ required: true, message: '请选择世界类型' }]}
          >
            <Select placeholder="选择世界的类型风格">
              <Option value="fantasy">奇幻</Option>
              <Option value="sci-fi">科幻</Option>
              <Option value="modern">现代</Option>
              <Option value="historical">历史</Option>
              <Option value="post-apocalyptic">末世</Option>
              <Option value="cyberpunk">赛博朋克</Option>
              <Option value="steampunk">蒸汽朋克</Option>
              <Option value="horror">恐怖</Option>
              <Option value="mystery">悬疑</Option>
              <Option value="adventure">冒险</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="setting"
            label="世界设定"
          >
            <Input placeholder="简要描述世界的背景设定（可选）" />
          </Form.Item>

          <Form.Item
            name="description"
            label={
              <Space>
                <span>世界描述</span>
                <AIButton
                  size="small"
                  icon={<RobotOutlined />}
                  loading={aiGenerating}
                  onClick={handleAIGenerate}
                >
                  AI辅助生成
                </AIButton>
              </Space>
            }
            rules={[
              { required: true, message: '请输入世界描述' },
              { min: 10, max: 1000, message: '世界描述长度应在10-1000个字符之间' }
            ]}
          >
            <TextArea
              rows={6}
              placeholder="详细描述你的世界，包括环境、文化、历史背景等..."
            />
          </Form.Item>

          <Divider />

          <Form.Item
            name="maxPlayers"
            label="最大玩家数"
            rules={[
              { required: true, message: '请设置最大玩家数' },
              { type: 'number', min: 1, max: 100, message: '玩家数应在1-100之间' }
            ]}
          >
            <Input type="number" min={1} max={100} />
          </Form.Item>

          <Form.Item
            name="isPublic"
            label="公开世界"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Text type="secondary" style={{ fontSize: 12 }}>
            公开世界将显示在世界列表中，其他玩家可以申请加入
          </Text>

          <Form.Item
            name="rules"
            label="世界规则"
            style={{ marginTop: 16 }}
          >
            <TextArea
              rows={4}
              placeholder="设定世界的游戏规则和限制（可选）"
            />
          </Form.Item>

          <Form.Item style={{ marginTop: 32, textAlign: 'center' }}>
            <Space size="large">
              <Button onClick={() => navigate('/lobby')}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
              >
                创建世界
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </StyledCard>
    </Container>
  )
}

export default WorldCreatePage
