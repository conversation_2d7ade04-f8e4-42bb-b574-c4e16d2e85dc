import React, { useEffect } from 'react'
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Typo<PERSON>, Empty, Spin } from 'antd'
import { useNavigate } from 'react-router-dom'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { 
  PlusOutlined, 
  UserOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  PlayCircleOutlined 
} from '@ant-design/icons'
import { useAppSelector, useAppDispatch } from '../store/hooks'
import { selectWorlds, selectGameLoading, setWorlds } from '../store/slices/gameSlice'
import { setPageTitle } from '../store/slices/uiSlice'
import { useGetMyWorldsQuery, useGetPublicWorldsQuery } from '../store/api'
import type { GameWorld } from '../store/slices/gameSlice'

const { Title, Paragraph, Text } = Typography

const LobbyContainer = styled.div`
  min-height: calc(100vh - 64px - 70px);
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
`

const LobbyContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`

const HeaderSection = styled.div`
  text-align: center;
  margin-bottom: 48px;
  
  .welcome-title {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
  }
`

const ActionSection = styled.div`
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
`

const WorldCard = styled(Card)`
  height: 100%;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
  
  .ant-card-meta-title {
    color: #1f2937;
    font-weight: 600;
  }
  
  .ant-card-meta-description {
    color: #6b7280;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`

const WorldStats = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
`

const StatItem = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  font-size: 12px;
  
  .anticon {
    color: #9ca3af;
  }
`

const CreateWorldCard = styled(Card)`
  height: 100%;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  background: #f9fafb;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #6366f1;
    background: #f8fafc;
    transform: translateY(-2px);
  }
  
  .ant-card-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 200px;
  }
  
  .create-icon {
    font-size: 48px;
    color: #6366f1;
    margin-bottom: 16px;
  }
`

const GameLobbyPage: React.FC = () => {
  const navigate = useNavigate()
  const dispatch = useAppDispatch()

  const worlds = useAppSelector(selectWorlds)
  const isLoading = useAppSelector(selectGameLoading)

  // 获取我的世界和公开世界
  const { data: myWorldsData, isLoading: myWorldsLoading } = useGetMyWorldsQuery({ page: 1, limit: 10 })
  const { data: publicWorldsData, isLoading: publicWorldsLoading } = useGetPublicWorldsQuery({ page: 1, limit: 20 })

  useEffect(() => {
    dispatch(setPageTitle('游戏大厅'))
  }, [dispatch])

  // 合并世界数据
  useEffect(() => {
    if (myWorldsData?.success && publicWorldsData?.success) {
      const allWorlds = [
        ...myWorldsData.data.worlds,
        ...publicWorldsData.data.worlds.filter(
          publicWorld => !myWorldsData.data.worlds.some(myWorld => myWorld.id === publicWorld.id)
        )
      ]
      dispatch(setWorlds(allWorlds))
    }
  }, [myWorldsData, publicWorldsData, dispatch])

  // 处理加入世界
  const handleJoinWorld = (world: GameWorld) => {
    navigate(`/game/${world.id}`)
  }

  // 处理查看世界详情
  const handleViewWorld = (world: GameWorld) => {
    // TODO: 打开世界详情模态框
    console.log('查看世界详情:', world)
  }

  // 检查是否正在加载
  const isLoadingData = myWorldsLoading || publicWorldsLoading || isLoading

  const displayWorlds = worlds

  return (
    <LobbyContainer>
      <LobbyContent>
        <HeaderSection>
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Title level={1} className="welcome-title">
              欢迎来到 AI 文本游戏世界
            </Title>
            <Paragraph style={{ fontSize: 16, color: '#6b7280' }}>
              选择一个世界开始你的冒险，或者创建属于你自己的故事
            </Paragraph>
          </motion.div>
        </HeaderSection>

        <ActionSection>
          <Title level={3} style={{ margin: 0 }}>
            可用世界
          </Title>
          <Button
            type="primary"
            size="large"
            icon={<PlusOutlined />}
            onClick={() => navigate('/world/create')}
          >
            创建新世界
          </Button>
        </ActionSection>

        {isLoadingData ? (
          <div style={{ textAlign: 'center', padding: '64px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16, color: '#6b7280' }}>
              正在加载世界列表...
            </div>
          </div>
        ) : (
          <Row gutter={[24, 24]}>
            {/* 创建世界卡片 */}
            <Col xs={24} sm={12} lg={8}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <CreateWorldCard onClick={() => navigate('/world/create')}>
                  <PlusOutlined className="create-icon" />
                  <Title level={4} style={{ margin: '0 0 8px 0' }}>
                    创建新世界
                  </Title>
                  <Text type="secondary">
                    发挥你的想象力，创造独特的冒险世界
                  </Text>
                </CreateWorldCard>
              </motion.div>
            </Col>

            {/* 世界列表 */}
            {displayWorlds.length > 0 ? (
              displayWorlds.map((world, index) => (
                <Col xs={24} sm={12} lg={8} key={world.id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <WorldCard
                      actions={[
                        <Button
                          key="view"
                          type="text"
                          icon={<EyeOutlined />}
                          onClick={() => handleViewWorld(world)}
                        >
                          查看
                        </Button>,
                        <Button
                          key="join"
                          type="primary"
                          icon={<PlayCircleOutlined />}
                          onClick={() => handleJoinWorld(world)}
                        >
                          加入
                        </Button>,
                      ]}
                    >
                      <Card.Meta
                        title={world.name}
                        description={world.description}
                      />
                      <WorldStats>
                        <StatItem>
                          <UserOutlined />
                          <span>{world.currentPlayers}/{world.maxPlayers}</span>
                        </StatItem>
                        <StatItem>
                          <ClockCircleOutlined />
                          <span>{new Date(world.createdAt).toLocaleDateString()}</span>
                        </StatItem>
                      </WorldStats>
                    </WorldCard>
                  </motion.div>
                </Col>
              ))
            ) : (
              <Col span={24}>
                <Empty
                  description="暂无可用世界"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                >
                  <Button type="primary" onClick={() => navigate('/world/create')}>
                    创建第一个世界
                  </Button>
                </Empty>
              </Col>
            )}
          </Row>
        )}
      </LobbyContent>
    </LobbyContainer>
  )
}

export default GameLobbyPage
