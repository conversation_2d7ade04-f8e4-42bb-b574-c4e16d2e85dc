package game

import (
	"context"
	"testing"

	"ai-text-game-iam-npc/internal/game"
	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// setupCharacterTestDB 创建角色测试数据库
func setupCharacterTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// 创建用户表
	err = db.Exec(`
		CREATE TABLE users (
			id TEXT PRIMARY KEY,
			external_id TEXT NOT NULL,
			external_provider TEXT NOT NULL,
			email TEXT NOT NULL,
			display_name TEXT,
			avatar_url TEXT,
			game_roles TEXT DEFAULT '["user"]',
			status TEXT DEFAULT 'active',
			preferences TEXT DEFAULT '{}',
			id_p_claims TEXT DEFAULT '{}',
			created_at DATETIME,
			updated_at DATETIME,
			last_login_at DATETIME,
			deleted_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建世界表
	err = db.Exec(`
		CREATE TABLE worlds (
			id TEXT PRIMARY KEY,
			name TEXT NOT NULL,
			description TEXT,
			creator_id TEXT NOT NULL,
			is_public BOOLEAN DEFAULT false,
			max_players INTEGER DEFAULT 10,
			current_players INTEGER DEFAULT 0,
			status TEXT DEFAULT 'active',
			world_config TEXT DEFAULT '{}',
			world_state TEXT DEFAULT '{}',
			game_time INTEGER DEFAULT 0,
			tick_count INTEGER DEFAULT 0,
			created_at DATETIME,
			updated_at DATETIME,
			deleted_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建角色表
	err = db.Exec(`
		CREATE TABLE characters (
			id TEXT PRIMARY KEY,
			world_id TEXT NOT NULL,
			user_id TEXT,
			name TEXT NOT NULL,
			description TEXT,
			character_type TEXT DEFAULT 'player',
			current_scene_id TEXT,
			traits TEXT DEFAULT '[]',
			memories TEXT DEFAULT '[]',
			experiences TEXT DEFAULT '[]',
			relationships TEXT DEFAULT '{}',
			status TEXT DEFAULT 'active',
			last_action_at DATETIME,
			created_at DATETIME,
			updated_at DATETIME,
			deleted_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	// 创建用户统计表
	err = db.Exec(`
		CREATE TABLE user_stats (
			user_id TEXT PRIMARY KEY,
			total_play_time INTEGER DEFAULT 0,
			worlds_created INTEGER DEFAULT 0,
			worlds_joined INTEGER DEFAULT 0,
			achievements TEXT DEFAULT '[]',
			level INTEGER DEFAULT 1,
			experience INTEGER DEFAULT 0,
			updated_at DATETIME
		)
	`).Error
	require.NoError(t, err)

	return db
}

// setupCharacterService 创建角色服务
func setupCharacterService(t *testing.T) (*game.CharacterService, *gorm.DB) {
	db := setupCharacterTestDB(t)
	log := logger.New("debug")
	service := game.NewCharacterService(db, log)
	return service, db
}

func TestNewCharacterService(t *testing.T) {
	db := setupCharacterTestDB(t)
	log := logger.New("debug")

	service := game.NewCharacterService(db, log)
	assert.NotNil(t, service)
}

func TestCreateCharacter(t *testing.T) {
	service, db := setupCharacterService(t)
	
	// 创建测试用户和世界
	userID := uuid.New()
	worldID := uuid.New()
	
	// 插入测试用户
	displayName := "Test User"
	err := db.Create(&models.User{
		ID:               userID,
		ExternalID:       "test-user",
		ExternalProvider: "test",
		Email:           "<EMAIL>",
		DisplayName:     &displayName,
	}).Error
	require.NoError(t, err)

	// 创建用户统计记录（如果不存在）
	var existingStats models.UserStats
	if err := db.Where("user_id = ?", userID).First(&existingStats).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			err = db.Create(&models.UserStats{
				UserID: userID,
			}).Error
			require.NoError(t, err)
		}
	}

	// 插入测试世界
	description := "这是一个测试世界"
	err = db.Create(&models.World{
		ID:          worldID,
		Name:        "测试世界",
		Description: &description,
		CreatorID:   userID,
	}).Error
	require.NoError(t, err)

	// 测试创建角色
	ctx := context.Background()
	character, err := service.CreateCharacter(ctx, worldID, &userID, "测试角色", "这是一个测试角色", "player", []string{"勇敢"})
	
	assert.NoError(t, err)
	assert.NotNil(t, character)
	assert.Equal(t, "测试角色", character.Name)
	assert.Equal(t, worldID, character.WorldID)
	assert.Equal(t, userID, *character.UserID)
}

func TestGetCharacter(t *testing.T) {
	service, db := setupCharacterService(t)
	
	// 创建测试数据
	userID := uuid.New()
	worldID := uuid.New()
	characterID := uuid.New()
	
	// 插入测试角色
	description := "这是一个测试角色"
	err := db.Session(&gorm.Session{SkipHooks: true}).Create(&models.Character{
		ID:            characterID,
		WorldID:       worldID,
		UserID:        &userID,
		Name:          "测试角色",
		Description:   &description,
		CharacterType: "player",
		Traits:        models.StringArray{},
		Memories:      models.JSON{},
		Experiences:   models.JSON{},
		Relationships: models.JSON{},
		Status:        "active",
	}).Error
	require.NoError(t, err)
	
	// 测试获取存在的角色
	ctx := context.Background()
	foundCharacter, err := service.GetCharacter(ctx, characterID)
	assert.NoError(t, err)
	assert.NotNil(t, foundCharacter)
	assert.Equal(t, characterID, foundCharacter.ID)
	assert.Equal(t, "测试角色", foundCharacter.Name)
	
	// 测试获取不存在的角色
	notFoundCharacter, err := service.GetCharacter(ctx, uuid.New())
	assert.Error(t, err)
	assert.Nil(t, notFoundCharacter)
}

func TestGetCharactersByWorld(t *testing.T) {
	service, db := setupCharacterService(t)
	
	// 创建测试数据
	userID := uuid.New()
	worldID := uuid.New()
	
	// 插入多个测试角色
	desc1 := "第一个角色"
	desc2 := "第二个角色"
	characters := []models.Character{
		{
			ID:            uuid.New(),
			WorldID:       worldID,
			UserID:        &userID,
			Name:          "角色1",
			Description:   &desc1,
			CharacterType: "player",
			Traits:        models.StringArray{},
			Memories:      models.JSON{},
			Experiences:   models.JSON{},
			Relationships: models.JSON{},
			Status:        "active",
		},
		{
			ID:            uuid.New(),
			WorldID:       worldID,
			UserID:        nil, // NPC
			Name:          "角色2",
			Description:   &desc2,
			CharacterType: "npc",
			Traits:        models.StringArray{},
			Memories:      models.JSON{},
			Experiences:   models.JSON{},
			Relationships: models.JSON{},
			Status:        "active",
		},
	}

	for _, char := range characters {
		err := db.Session(&gorm.Session{SkipHooks: true}).Create(&char).Error
		require.NoError(t, err)
	}
	
	// 测试获取世界中的角色
	ctx := context.Background()
	foundCharacters, total, err := service.GetCharactersByWorld(ctx, worldID, "", 10, 0)
	assert.NoError(t, err)
	assert.Len(t, foundCharacters, 2)
	assert.Equal(t, int64(2), total)
}

func TestUpdateCharacter(t *testing.T) {
	service, db := setupCharacterService(t)
	
	// 创建测试数据
	userID := uuid.New()
	worldID := uuid.New()
	characterID := uuid.New()
	
	// 插入测试角色（跳过钩子）
	description := "原始描述"
	err := db.Session(&gorm.Session{SkipHooks: true}).Create(&models.Character{
		ID:            characterID,
		WorldID:       worldID,
		UserID:        &userID,
		Name:          "原始角色",
		Description:   &description,
		CharacterType: "player",
		Traits:        models.StringArray{},
		Memories:      models.JSON{},
		Experiences:   models.JSON{},
		Relationships: models.JSON{},
		Status:        "active",
	}).Error
	require.NoError(t, err)

	// 创建用户统计记录
	err = db.Create(&models.UserStats{
		UserID: userID,
	}).Error
	require.NoError(t, err)

	// 测试更新角色
	ctx := context.Background()
	err = service.UpdateCharacter(ctx, characterID, "更新后的角色", "更新后的描述", []string{"勇敢", "智慧"}, &userID)

	assert.NoError(t, err)

	// 验证更新结果
	var updatedCharacter models.Character
	err = db.Where("id = ?", characterID).First(&updatedCharacter).Error
	assert.NoError(t, err)
	assert.Equal(t, "更新后的角色", updatedCharacter.Name)
	assert.Equal(t, "更新后的描述", *updatedCharacter.Description)
}

func TestDeleteCharacter(t *testing.T) {
	service, db := setupCharacterService(t)
	
	// 创建测试数据
	userID := uuid.New()
	worldID := uuid.New()
	characterID := uuid.New()
	
	// 插入测试角色（跳过钩子）
	description := "这个角色将被删除"
	err := db.Session(&gorm.Session{SkipHooks: true}).Create(&models.Character{
		ID:            characterID,
		WorldID:       worldID,
		UserID:        &userID,
		Name:          "待删除角色",
		Description:   &description,
		CharacterType: "player",
		Traits:        models.StringArray{},
		Memories:      models.JSON{},
		Experiences:   models.JSON{},
		Relationships: models.JSON{},
		Status:        "active",
	}).Error
	require.NoError(t, err)

	// 创建用户统计记录
	err = db.Create(&models.UserStats{
		UserID: userID,
	}).Error
	require.NoError(t, err)

	// 测试删除角色
	ctx := context.Background()
	err = service.DeleteCharacter(ctx, characterID, &userID)
	assert.NoError(t, err)
	
	// 验证角色已被软删除
	var character models.Character
	err = db.Unscoped().Where("id = ?", characterID).First(&character).Error
	assert.NoError(t, err)
	assert.NotNil(t, character.DeletedAt)
}
